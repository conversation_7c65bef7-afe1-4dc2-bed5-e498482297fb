/* Hyprland Modern Style - Smooth & Responsive */
* {
    font-size: 11pt;
    font-family: "JetBrainsMono Nerd Font", "Font Awesome 6 Free";
    font-weight: 500;
    border: none;
    border-radius: 12px;
    transition: all 0.3s cubic-bezier(0.4, 0.0, 0.2, 1);
}

window#waybar {
    background: rgba(30, 30, 46, 0.85);
    backdrop-filter: blur(10px);
    color: #cdd6f4;
    border: 1px solid rgba(116, 199, 236, 0.3);
    border-radius: 16px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

/* Module groups with modern styling */
.modules-left, .modules-center, .modules-right {
    background: transparent;
    margin: 4px;
}

/* Individual modules */
#workspaces, #window, #clock, #cpu, #memory, #network, #pulseaudio, #tray, #privacy, #idle_inhibitor {
    background: rgba(49, 50, 68, 0.6);
    color: #cdd6f4;
    border-radius: 10px;
    margin: 2px 3px;
    padding: 6px 12px;
    border: 1px solid rgba(116, 199, 236, 0.2);
    transition: all 0.3s ease;
}

/* Hover effects */
#workspaces:hover, #window:hover, #clock:hover, #cpu:hover, #memory:hover, #network:hover, #pulseaudio:hover {
    background: rgba(116, 199, 236, 0.2);
    border-color: rgba(116, 199, 236, 0.5);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(116, 199, 236, 0.3);
}

/* Workspace specific styling */
#workspaces button {
    background: rgba(49, 50, 68, 0.4);
    color: #6c7086;
    border-radius: 8px;
    margin: 0 2px;
    padding: 4px 8px;
    border: 1px solid transparent;
    transition: all 0.2s ease;
}

#workspaces button.active {
    background: rgba(116, 199, 236, 0.3);
    color: #74c7ec;
    border-color: rgba(116, 199, 236, 0.5);
}

#workspaces button:hover {
    background: rgba(116, 199, 236, 0.15);
    color: #74c7ec;
}
