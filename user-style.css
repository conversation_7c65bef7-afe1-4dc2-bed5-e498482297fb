/* Clean Style - Solid Background, Individual Buttons */
* {
    font-size: 11pt;
    font-family: "JetBrainsMono Nerd Font", "Font Awesome 6 Free";
    border: none;
    border-radius: 8px;
}

window#waybar {
    background: #1e1e2e;
    color: #cdd6f4;
    border: none;
    border-radius: 0;
}

/* Module groups - no background, just spacing */
.modules-left, .modules-center, .modules-right {
    background: transparent;
    margin: 4px;
}

/* Individual modules - separate buttons */
#workspaces, #window, #clock, #cpu, #memory, #network, #pulseaudio, #tray, #privacy, #idle_inhibitor, #backlight, #custom-updates, #custom-keybindhint, #custom-wallchange, #custom-theme, #custom-wbar, #custom-cliphist, #custom-hyde-menu, #custom-power {
    background: #313244;
    color: #cdd6f4;
    border-radius: 8px;
    margin: 2px;
    padding: 6px 12px;
    border: 1px solid #45475a;
}

/* Hover effects */
#workspaces:hover, #window:hover, #clock:hover, #cpu:hover, #memory:hover, #network:hover, #pulseaudio:hover, #tray:hover, #privacy:hover, #idle_inhibitor:hover, #backlight:hover, #custom-updates:hover, #custom-keybindhint:hover, #custom-wallchange:hover, #custom-theme:hover, #custom-wbar:hover, #custom-cliphist:hover, #custom-hyde-menu:hover, #custom-power:hover {
    background: #45475a;
    border-color: #74c7ec;
}

/* Workspace buttons */
#workspaces button {
    background: #45475a;
    color: #6c7086;
    border-radius: 6px;
    margin: 0 2px;
    padding: 4px 8px;
    border: 1px solid #585b70;
}

#workspaces button.active {
    background: #74c7ec;
    color: #1e1e2e;
    border-color: #74c7ec;
}

#workspaces button:hover {
    background: #585b70;
    color: #cdd6f4;
}
