{"layer": "top", "output": ["*"], "height": 10, "exclusive": true, "passthrough": false, "reload_style_on_change": true, "include": ["$XDG_CONFIG_HOME/waybar/modules/*json*", "$XDG_CONFIG_HOME/waybar/includes/includes.json"], "modules-left": ["group/pill#left1", "group/pill#left2"], "group/pill#left1": {"orientation": "inherit", "modules": ["cpu", "memory"]}, "group/pill#left2": {"orientation": "inherit", "modules": ["idle_inhibitor", "clock"]}, "modules-center": ["group/pill#center"], "group/pill#center": {"modules": ["hyprland/workspaces", "hyprland/window"], "orientation": "inherit"}, "modules-right": ["group/pill#right1", "group/pill#right2", "group/pill#right3"], "group/pill#right1": {"modules": ["backlight", "network", "pulseaudio", "pulseaudio#microphone", "custom/updates", "custom/keybindhint"], "orientation": "inherit"}, "group/pill#right2": {"modules": ["privacy", "tray"], "orientation": "inherit"}, "group/pill#right3": {"modules": ["custom/wallchange", "custom/theme", "custom/wbar", "custom/cliphist", "custom/hyde-menu", "custom/power"], "orientation": "inherit"}}