#!/bin/bash

# Script untuk toggle visibility IP address di waybar
# File untuk menyimpan status visibility
STATUS_FILE="$HOME/.config/waybar/.ip_visible"
CONFIG_FILE="$HOME/.config/waybar/config.jsonc"

# Cek apakah IP sedang visible atau tidak
if [ -f "$STATUS_FILE" ]; then
    # IP sedang visible, sembunyikan
    rm "$STATUS_FILE"
    # Update konfigurasi untuk menyembunyikan IP
    sed -i 's/"custom\/ip-address",//g' "$CONFIG_FILE"
    sed -i 's/,"custom\/ip-address"//g' "$CONFIG_FILE"
    notify-send "Waybar" "IP address disembunyikan" -t 2000
else
    # IP sedang tersembunyi, tampilkan
    touch "$STATUS_FILE"
    # Update konfigurasi untuk menampilkan IP (pastikan ada setelah ip-toggle)
    if ! grep -q '"custom/ip-address"' "$CONFIG_FILE"; then
        sed -i '/custom\/ip-toggle/a\            "custom\/ip-address",' "$CONFIG_FILE"
    fi
    notify-send "Waybar" "IP address ditampilkan" -t 2000
fi

# Restart waybar
pkill waybar
sleep 0.5
waybar &
